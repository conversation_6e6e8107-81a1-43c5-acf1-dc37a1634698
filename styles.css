/* Root Variables */
:root {
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --secondary-color: #1e293b;
    --accent-color: #f59e0b;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --bg-light: #f8fafc;
    --bg-dark: #0f172a;
    --white: #ffffff;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    --gradient-secondary: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    overflow-x: hidden;
}

html {
    scroll-behavior: smooth;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

/* Navigation */
.navbar {
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    padding: 1rem 0;
}

.navbar.scrolled {
    background: rgba(15, 23, 42, 0.98);
    box-shadow: var(--shadow-md);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--white) !important;
    text-decoration: none;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500;
    margin: 0 0.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    transform: translateY(-2px);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 50%;
    background: var(--primary-color);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
    width: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
    border-radius: 50px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: var(--primary-dark);
}

.btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: var(--white);
    border-radius: 50px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-outline-light:hover {
    background: var(--white);
    color: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Hero Section */
.hero-section {
    background: var(--gradient-secondary);
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%" r="50%"><stop offset="0%" stop-color="%233b82f6" stop-opacity="0.1"/><stop offset="100%" stop-color="%233b82f6" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>') no-repeat center center;
    background-size: cover;
    opacity: 0.5;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2.5rem;
    max-width: 500px;
}

.hero-buttons {
    margin-bottom: 3rem;
}

.hero-stats {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
}

/* Hero Image */
.hero-image {
    position: relative;
    height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.floating-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    color: var(--white);
    font-weight: 500;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
}

.floating-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

.floating-card i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.card-1 {
    top: 20%;
    left: 10%;
    animation: float 6s ease-in-out infinite;
}

.card-2 {
    top: 50%;
    right: 10%;
    animation: float 6s ease-in-out infinite 2s;
}

.card-3 {
    bottom: 20%;
    left: 20%;
    animation: float 6s ease-in-out infinite 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
}

.scroll-arrow {
    width: 2px;
    height: 30px;
    background: var(--white);
    position: relative;
    animation: scroll 2s infinite;
}

.scroll-arrow::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: -3px;
    width: 8px;
    height: 8px;
    border-right: 2px solid var(--white);
    border-bottom: 2px solid var(--white);
    transform: rotate(45deg);
}

@keyframes scroll {
    0%, 100% { opacity: 0; transform: translateY(-10px); }
    50% { opacity: 1; transform: translateY(0); }
}

/* About Section */
.about-section {
    background: var(--bg-light);
    padding: 5rem 0;
}

.about-content {
    padding-right: 2rem;
}

.about-features {
    margin-top: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    font-weight: 500;
}

.feature-item i {
    font-size: 1.25rem;
}

.about-image {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400px;
}

.image-placeholder {
    width: 300px;
    height: 300px;
    background: var(--gradient-primary);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    box-shadow: var(--shadow-xl);
}

/* Services Section */
.services-section {
    padding: 5rem 0;
    background: var(--white);
}

.service-card {
    background: var(--white);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    height: 100%;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: var(--white);
    font-size: 2rem;
}

.service-card h4 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.service-card p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.service-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.service-link:hover {
    color: var(--primary-dark);
    text-decoration: none;
}

/* Contact Section */
.contact-section {
    background: var(--bg-light);
    padding: 5rem 0;
}

.contact-info {
    padding-right: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.25rem;
    flex-shrink: 0;
}

.contact-details h5 {
    font-size: 1.125rem;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.contact-details p {
    color: var(--text-secondary);
    margin: 0;
}

.contact-form {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
}

.form-control {
    border: 2px solid var(--border-color);
    border-radius: 10px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
}

/* Footer */
.footer {
    background: var(--bg-dark);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-brand h4 {
    color: var(--white);
    margin-bottom: 1rem;
}

.footer-brand p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 1.5rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

.footer-links h5 {
    color: var(--white);
    margin-bottom: 1rem;
    font-size: 1.125rem;
}

.footer-links ul {
    list-style: none;
    padding: 0;
}

.footer-links ul li {
    margin-bottom: 0.5rem;
}

.footer-links ul li a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer-links ul li a:hover {
    color: var(--primary-color);
}

.footer-divider {
    border-color: rgba(255, 255, 255, 0.1);
    margin: 2rem 0 1rem;
}

.copyright, .disclaimer {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.875rem;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 991.98px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .about-content {
        padding-right: 0;
        margin-bottom: 2rem;
    }
    
    .contact-info {
        padding-right: 0;
        margin-bottom: 2rem;
    }
    
    .floating-card {
        position: static;
        margin: 1rem 0;
        transform: none !important;
    }
    
    .hero-image {
        height: auto;
        flex-direction: column;
        margin-top: 2rem;
    }
}

@media (max-width: 767.98px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 1.75rem;
    }
    
    .service-card {
        padding: 2rem;
    }
    
    .contact-form {
        padding: 2rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
}

.fade-in-left {
    opacity: 0;
    transform: translateX(-30px);
}

.fade-in-right {
    opacity: 0;
    transform: translateX(30px);
}

.scale-in {
    opacity: 0;
    transform: scale(0.8);
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background: var(--gradient-primary) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}
